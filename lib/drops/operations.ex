defmodule Drops.Operations do
  use Drops.Operations.Core,
    type: :abstract,
    extensions: [
      Drops.Operations.Extensions.Command,
      Drops.Operations.Extensions.Params,
      Drops.Operations.Extensions.Ecto
    ]

  defmacro __using__(opts) do
    # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

    extend(__MODULE__, __CALLER__.module, opts)
  end

  defmacro __before_compile__(env) do
    IO.puts("#{__MODULE__}.__before_compile__(#{inspect(env.module)})")

    module = env.module

    enabled_extensions =
      Enum.filter(Module.get_attribute(module, :extensions), fn extension ->
        extension.enabled?(Module.get_attribute(module, :opts))
      end)

    Module.put_attribute(module, :enabled_extensions, enabled_extensions)

    extension_code =
      Enum.map(enabled_extensions, fn extension ->
        quote do
          use unquote(extension)
        end
      end)

    extension_steps = Enum.map(enabled_extensions, fn extension -> extension.steps() end)
    custom_steps = Module.get_attribute(module, :steps, [])

    quote do
      unquote_splicing(extension_code)

      unquote_splicing(extension_steps)

      unquote(custom_steps)

      def __unit_of_work__, do: @unit_of_work
    end
  end

  def extend(source_module, _target_module, opts) do
    # IO.puts("#{__MODULE__}.extend(#{inspect(source_module)}, #{inspect(target_module)}, #{inspect(opts)})")

    quote do
      use Drops.Operations.Core, unquote(Keyword.put(opts, :source_module, source_module))
      use Drops.Contract

      schema do
        %{}
      end

      @before_compile Drops.Operations

      import Drops.Operations

      defmacro __using__(opts) do
        # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

        extend(__MODULE__, __CALLER__.module, opts)
      end

      def call(context) do
        Drops.Operations.UnitOfWork.process(__unit_of_work__(), context)
      end

      def conform(%{params: params} = context) do
        case super(params) do
          {:ok, conformed_params} ->
            {:ok, Map.put(context, :params, conformed_params)}

          {:error, _} = error ->
            error
        end
      end
    end
  end
end
