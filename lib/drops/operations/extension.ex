defmodule Drops.Operations.Extension do
  @callback enabled?(opts :: keyword()) :: boolean()

  @callback unit_of_work(uow :: map(), opts :: keyword()) :: map()

  @callback extend(extension :: module(), operation :: module(), opts :: keyword()) ::
              Macro.t()

  defmacro __using__(opts) do
    quote do
      @behaviour Drops.Operations.Extension

      import Drops.Operations.Core, only: [steps: 1]

      @opts unquote(opts)
      def __opts__, do: @opts

      def enabled?(_opts), do: true
      defoverridable enabled?: 1

      def unit_of_work(uow, _opts), do: uow
      defoverridable unit_of_work: 2

      def helpers, do: []
      defoverridable helpers: 0

      def steps, do: []
      defoverridable steps: 0

      defmacro __using__(opts) do
        ext_module = __MODULE__
        ext_code = ext_module.extend(__MODULE__, __CALLER__.module, opts)
        base_code = Drops.Operations.Extension.extend(ext_module, __CALLER__.module, opts)

        IO.puts("Applying extension #{ext_module} to #{__CALLER__.module}")

        quote do
          unquote(base_code)
          unquote(ext_code)

          @before_compile unquote(ext_module)
        end
      end
    end
  end

  def extend(extension, operation, opts) do
    IO.puts(
      "BASE #{__MODULE__}.extend(#{inspect(operation)}, #{inspect(opts)}) with #{extension}"
    )

    unit_of_work =
      extension.unit_of_work(Module.get_attribute(operation, :unit_of_work), opts)

    Module.put_attribute(operation, :unit_of_work, unit_of_work)
  end
end
