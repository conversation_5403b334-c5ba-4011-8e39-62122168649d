defmodule Drops.Operations.Core do
  defmacro __using__(opts) do
    # IO.puts("#{__MODULE__}.__using__(#{inspect(opts)})")

    quote do
      import Drops.Operations.Core

      if unquote(opts[:source_module]) do
        @opts Drops.Operations.Core.merge_opts(
                unquote(opts[:source_module]).__opts__(),
                unquote(opts)
              )
        @source_module unquote(opts[:source_module])
      else
        @opts unquote(opts)
        @source_module nil
      end

      def __opts__, do: @opts

      @extensions @opts[:extensions]
      def __extensions__, do: @extensions

      @unit_of_work Drops.Operations.UnitOfWork.new(__MODULE__, [])

      @before_compile Drops.Operations.Core
    end
  end

  defmacro __before_compile__(_env) do
    # IO.inspect(env.module, label: "#{__MODULE__}.__before_compile__")
  end

  defmacro steps(do: block) do
    quote do
      @steps unquote(Macro.escape(block))

      def steps, do: @steps
    end
  end

  def merge_opts(parent_opts, new_opts) do
    extensions =
      Keyword.get(new_opts, :extensions, []) ++ Keyword.get(parent_opts, :extensions, [])

    Keyword.merge(parent_opts, new_opts) |> Keyword.put(:extensions, extensions)
  end
end
