defmodule Drops.Operations.Extensions.Params do
  use Drops.Operations.Extension

  defmacro __before_compile__(env) do
    IO.puts("#{__MODULE__}.__before_compile__(#{inspect(env.module)})")

    quote do
    end
  end

  @impl true
  def unit_of_work(uow, _opts) do
    # Add conform as the first step in the pipeline
    updated_steps = Map.put(uow.steps, :conform, {uow.module, :conform})
    updated_step_order = [:conform | uow.step_order]
    %{uow | steps: updated_steps, step_order: updated_step_order}
  end

  @impl true
  def setup(_, _, _) do
    quote do
      use Drops.Contract

      schema do
        %{}
      end
    end
  end

  @impl true
  def extend(_, _, _) do
    quote do
    end
  end

  steps do
    quote do
      def conform(%{params: params} = context) do
        IO.puts("Params.conform called with context: #{inspect(context)}")
        IO.puts("Calling super with params: #{inspect(params)}")

        try do
          case super(params) do
            {:ok, conformed_params} ->
              IO.puts("Super returned conformed_params: #{inspect(conformed_params)}")
              {:ok, Map.put(context, :params, conformed_params)}

            {:error, _} = error ->
              IO.puts("Super returned error: #{inspect(error)}")
              error
          end
        rescue
          e ->
            IO.puts("Exception calling super: #{inspect(e)}")
            {:error, e}
        end
      end
    end
  end
end
