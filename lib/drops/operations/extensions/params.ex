defmodule Drops.Operations.Extensions.Params do
  use Drops.Operations.Extension

  defmacro __before_compile__(env) do
    IO.puts("#{__MODULE__}.__before_compile__(#{inspect(env.module)})")

    quote do
    end
  end

  @impl true
  def unit_of_work(uow, _opts) do
    uow
    |> Drops.Operations.UnitOfWork.before_step(:prepare, :conform)
  end

  @impl true
  def extend(_, _, _) do
    quote do
    end
  end

  steps do
    quote do
    end
  end
end
